"""
Deploy script for ACT model using xrocs API.
This script:
1. Captures images using xrocs camera
2. Performs inference using ACT model
3. Controls robot using xrocs API
"""

import sys
from pathlib import Path
import time
import numpy as np
import torch
import argparse
import cv2
import os

# Import directly from the installed package
from xrocs.core.config_loader import ConfigLoader
from xrocs.core.station_loader import StationLoader
from xrocs.utils.logger.logger_loader import logger

from lerobot.policies.act.modeling_act import ACTPolicy
import einops

'''
Example usage:

Basic usage:
python3 act_deploy.py /home/<USER>/Documents/configuration.toml outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/260000

With debug mode (saves camera images and state info):
python3 act_deploy.py /home/<USER>/Documents/configuration.toml outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/260000 --debug

With real-time image display:
python3 act_deploy.py /home/<USER>/Documents/configuration.toml outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/260000 --show_images

With both debug and image display:
python3 act_deploy.py /home/<USER>/Documents/configuration.toml outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/260000 --debug --show_images

Use the 10th action from chunk (index 9):
python3 act_deploy.py /home/<USER>/Documents/configuration.toml outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/260000 --action_index 9 --debug

With custom parameters:
python3 act_deploy.py /home/<USER>/Documents/configuration.toml outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/010000 --max_steps 500 --interval 0.1 --debug --show_images --action_index 9

The checkpoint directory should contain:
- pretrained_model/
    - config.json
    - model.safetensors
    - train_config.json
'''
def preprocess_xrocs_observation(obs, debug=False, debug_dir="debug_images"):
    """
    Convert xROCS observation to ACT model format

    Args:
        obs: Dictionary from robot_station.get_obs() containing:
            - images: Dict of camera images (numpy arrays)
            - depths: Dict of depth images (numpy arrays)
            - arm_joints: Dict of arm joint positions
            - hand_joints: Dict of hand joint positions
            - arm_pose: Dict of arm poses (optional)
        debug: If True, save debug information and images
        debug_dir: Directory to save debug images

    Returns:
        Dictionary with processed observations in ACT format
    """
    processed_obs = {}

    # Create debug directory if needed
    if debug and not os.path.exists(debug_dir):
        os.makedirs(debug_dir)
        logger.info(f"Created debug directory: {debug_dir}")

    # Process images
    main_camera_processed = False
    if "images" in obs and obs["images"]:
        logger.info(f"Processing {len(obs['images'])} camera images")

        for cam_name, img in obs["images"].items():
            logger.info(f"Processing camera {cam_name}: type={type(img)}")
            if img is None:
                logger.warning(f"Camera {cam_name} returned None - skipping")
                continue
            elif not isinstance(img, np.ndarray):
                logger.warning(f"Camera {cam_name} returned unexpected type {type(img)} - skipping")
                continue

            logger.info(f"Camera {cam_name}: shape={img.shape}, dtype={img.dtype}, range=[{img.min()}, {img.max()}]")

            # Check if image is encoded (1D array) and needs decoding
            if img.ndim == 1:
                # Image is likely encoded as JPEG bytes, decode it
                try:
                    img = cv2.imdecode(img, cv2.IMREAD_COLOR)
                    if img is None:
                        logger.error(f"Failed to decode image from camera {cam_name}")
                        continue
                    logger.info(f"Decoded {cam_name}: shape={img.shape}, dtype={img.dtype}")
                except Exception as e:
                    logger.error(f"Error decoding image from camera {cam_name}: {e}")
                    continue
            elif img.ndim != 3 or img.shape[2] != 3:
                logger.error(f"Unexpected image format from camera {cam_name}: shape={img.shape}")
                continue

            # Save original image for debugging
            if debug:
                debug_img_path = os.path.join(debug_dir, f"original_{cam_name}.jpg")
                # Save image directly - xROCS cameras usually provide BGR format
                if img.shape[2] == 3:
                    # Try to save as-is first (assuming BGR from camera)
                    cv2.imwrite(debug_img_path, img)
                    logger.info(f"Saved original image: {debug_img_path}")

                    # Also save RGB version for comparison
                    debug_img_rgb_path = os.path.join(debug_dir, f"original_{cam_name}_RGB.jpg")
                    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    img_rgb_bgr = cv2.cvtColor(img_rgb, cv2.COLOR_RGB2BGR)
                    cv2.imwrite(debug_img_rgb_path, img_rgb_bgr)
                    logger.info(f"Saved RGB converted image: {debug_img_rgb_path}")
                else:
                    cv2.imwrite(debug_img_path, img)
                    logger.info(f"Saved original image: {debug_img_path}")

            # Convert to torch tensor
            img_tensor = torch.from_numpy(img)

            # Ensure image is in correct format
            if img_tensor.ndim == 3:  # H, W, C
                # Convert to channel first: C, H, W
                img_tensor = einops.rearrange(img_tensor, "h w c -> c h w")
            elif img_tensor.ndim == 4:  # B, H, W, C
                # Convert to channel first: B, C, H, W
                img_tensor = einops.rearrange(img_tensor, "b h w c -> b c h w")
            else:
                logger.error(f"Unexpected tensor dimensions for {cam_name}: {img_tensor.shape}")
                continue

            # Normalize to [0, 1] if needed
            if img_tensor.dtype == torch.uint8:
                img_tensor = img_tensor.float() / 255.0

            # Add batch dimension if needed
            if img_tensor.ndim == 3:
                img_tensor = img_tensor.unsqueeze(0)

            logger.info(f"Processed {cam_name}: shape={img_tensor.shape}, dtype={img_tensor.dtype}, range=[{img_tensor.min():.3f}, {img_tensor.max():.3f}]")

            # Save processed image for debugging
            if debug:
                # Convert back to numpy for saving (remove batch dim, channel last, scale to 0-255)
                debug_tensor = img_tensor.squeeze(0)  # Remove batch dim
                debug_tensor = einops.rearrange(debug_tensor, "c h w -> h w c")  # Channel last
                debug_img = (debug_tensor.numpy() * 255).astype(np.uint8)
                debug_img_path = os.path.join(debug_dir, f"processed_{cam_name}.jpg")
                debug_img_bgr = cv2.cvtColor(debug_img, cv2.COLOR_RGB2BGR)
                cv2.imwrite(debug_img_path, debug_img_bgr)
                logger.info(f"Saved processed image: {debug_img_path}")

            # Use 'main' as the primary camera key name for ACT model compatibility
            if cam_name in ['camera_front', 'front', 'main', 'top'] or len(obs["images"]) == 1:
                # Use the first/main camera as 'main'
                processed_obs["observation.images.main"] = img_tensor
                main_camera_processed = True
                logger.info(f"Using {cam_name} as main camera for ACT model")
            else:
                # For additional cameras, still save with original names
                processed_obs[f"observation.images.{cam_name}"] = img_tensor

    # Ensure we have a main camera
    if not main_camera_processed:
        logger.error("No main camera was processed! Available cameras: " + str(list(obs.get("images", {}).keys())))
        raise ValueError("No main camera available for ACT model")

    # Process robot state (arm joints, hand joints, etc.)
    state_values = []
    state_info = []  # For debug logging

    # Add arm joint positions
    if "arm_joints" in obs and obs["arm_joints"]:
        logger.info(f"Processing arm joints: {list(obs['arm_joints'].keys())}")
        for arm_name, joints in obs["arm_joints"].items():
            if isinstance(joints, np.ndarray):
                state_values.extend(joints.flatten())
                state_info.append(f"arm_joints.{arm_name}: {joints.flatten()}")
                if debug:
                    logger.info(f"  {arm_name} joints: {joints}")
            elif isinstance(joints, (list, tuple)):
                state_values.extend(joints)
                state_info.append(f"arm_joints.{arm_name}: {joints}")
                if debug:
                    logger.info(f"  {arm_name} joints: {joints}")

    # Add hand joint positions
    if "hand_joints" in obs and obs["hand_joints"]:
        logger.info(f"Processing hand joints: {list(obs['hand_joints'].keys())}")
        for hand_name, joints in obs["hand_joints"].items():
            if isinstance(joints, np.ndarray):
                state_values.extend(joints.flatten())
                state_info.append(f"hand_joints.{hand_name}: {joints.flatten()}")
                if debug:
                    logger.info(f"  {hand_name} joints: {joints}")
            elif isinstance(joints, (list, tuple)):
                state_values.extend(joints)
                state_info.append(f"hand_joints.{hand_name}: {joints}")
                if debug:
                    logger.info(f"  {hand_name} joints: {joints}")

    # Add arm pose if available
    if "arm_pose" in obs and obs["arm_pose"]:
        logger.info(f"Processing arm poses: {list(obs['arm_pose'].keys())}")
        for arm_name, pose in obs["arm_pose"].items():
            if isinstance(pose, np.ndarray):
                state_values.extend(pose.flatten())
                state_info.append(f"arm_pose.{arm_name}: {pose.flatten()}")
                if debug:
                    logger.info(f"  {arm_name} pose: {pose}")
            elif isinstance(pose, (list, tuple)):
                state_values.extend(pose)
                state_info.append(f"arm_pose.{arm_name}: {pose}")
                if debug:
                    logger.info(f"  {arm_name} pose: {pose}")

    # Create state tensor
    if state_values:
        state_tensor = torch.tensor(state_values, dtype=torch.float32)
        if state_tensor.ndim == 1:
            state_tensor = state_tensor.unsqueeze(0)  # Add batch dimension
        processed_obs["observation.state"] = state_tensor

        logger.info(f"Final state tensor: shape={state_tensor.shape}, dtype={state_tensor.dtype}")
        if debug:
            logger.info(f"State values: {state_values}")
            # Save state info to file
            state_file = os.path.join(debug_dir, "state_info.txt")
            with open(state_file, "w") as f:
                f.write("Robot State Information:\n")
                f.write("=" * 50 + "\n")
                for info in state_info:
                    f.write(info + "\n")
                f.write(f"\nFinal state vector length: {len(state_values)}\n")
                f.write(f"State tensor shape: {state_tensor.shape}\n")
            logger.info(f"Saved state info: {state_file}")

    return processed_obs


def load_act_model(checkpoint_dir):
    """
    Load ACT model from checkpoint directory using from_pretrained method
    Args:
        checkpoint_dir (str): Directory containing pretrained_model with config.json and model.safetensors
    """
    pretrained_model_dir = Path(checkpoint_dir) / "pretrained_model"

    # Check if directory exists
    if not pretrained_model_dir.exists():
        raise FileNotFoundError(f"Pretrained model directory not found: {pretrained_model_dir}")

    logger.info(f"Loading ACT model from: {pretrained_model_dir}")

    try:
        # Use the from_pretrained method like in act_eval_episode.py
        policy = ACTPolicy.from_pretrained(str(pretrained_model_dir))
        policy.eval()
        logger.info("Successfully loaded ACT model using from_pretrained method")

        # Print model info
        logger.info(f"Model type: {type(policy).__name__}")
        logger.info(f"Chunk size: {policy.config.chunk_size}")
        logger.info(f"Action steps: {policy.config.n_action_steps}")
        logger.info(f"Model parameters: {sum(p.numel() for p in policy.parameters()):,}")

        return policy

    except Exception as e:
        logger.error(f"Failed to load ACT model: {e}")
        raise


def run_act_deploy(xrocs_config_path, act_checkpoint_dir, max_steps=1000, interval=0.05, debug=False, show_images=False, action_index=0, action_scale=1.0):
    """
    Main deployment loop for ACT model using xrocs

    Args:
        xrocs_config_path (str): Path to xrocs configuration file
        act_checkpoint_dir (str): Directory containing ACT model checkpoint (with pretrained_model subdirectory)
        max_steps (int): Maximum number of steps to run
        interval (float): Time interval between steps
        debug (bool): Enable debug mode with image saving and detailed logging
        show_images (bool): Show camera images in real-time windows
        action_index (int): Which action to use from the chunk (0-19, default: 0 for first action)
    """
    # Initialize xrocs
    cfg_loader = ConfigLoader(xrocs_config_path)
    cfg_dict = cfg_loader.get_config()
    station_loader = StationLoader(cfg_dict)
    robot_station = station_loader.generate_station_handle()
    robot_station.connect()

    # Check connection status and diagnose robot state
    logger.info("=== xROCS System Status ===")
    logger.info(f"Robot station type: {type(robot_station)}")

    # Check if robot_station has status methods
    available_methods = [method for method in dir(robot_station) if not method.startswith('_')]
    logger.info(f"Available methods: {available_methods}")

    # Diagnose robot connection issues
    logger.info("=== Robot Connection Diagnostics ===")

    # Check individual robot connections
    if hasattr(robot_station, '_robot_dict'):
        logger.info(f"Robot dictionary keys: {list(robot_station._robot_dict.keys())}")
        for robot_name, robot in robot_station._robot_dict.items():
            logger.info(f"Robot '{robot_name}': type={type(robot)}")

            # Check if robot has joint position data
            if hasattr(robot, 'left_jpos') and hasattr(robot, 'right_jpos'):
                logger.info(f"  Left joint positions: {getattr(robot, 'left_jpos', 'Not available')}")
                logger.info(f"  Right joint positions: {getattr(robot, 'right_jpos', 'Not available')}")

                # Try to get current joint directly
                try:
                    current_joint = robot.get_current_joint()
                    if current_joint is None:
                        logger.error(f"  Robot '{robot_name}' get_current_joint() returned None!")
                        logger.error(f"  This usually means the robot is not receiving joint position data from ROS2 topics")
                        logger.error(f"  Check if the robot hardware is connected and ROS2 topics are publishing")
                    else:
                        logger.info(f"  Robot '{robot_name}' joint data: {current_joint.get_radian_ndarray()}")
                except Exception as e:
                    logger.error(f"  Error getting joints from robot '{robot_name}': {e}")

    # Check hand connections
    if hasattr(robot_station, '_hand_dict'):
        logger.info(f"Hand dictionary keys: {list(robot_station._hand_dict.keys())}")
        for hand_name, hand in robot_station._hand_dict.items():
            logger.info(f"Hand '{hand_name}': type={type(hand)}")
            try:
                current_joint = hand.get_current_joint()
                if current_joint is None:
                    logger.error(f"  Hand '{hand_name}' get_current_joint() returned None!")
                    logger.error(f"  This usually means the hand/gripper is not connected or not responding")
                else:
                    logger.info(f"  Hand '{hand_name}' joint data: {current_joint.get_radian_ndarray()}")
            except Exception as e:
                logger.error(f"  Error getting joints from hand '{hand_name}': {e}")

    # Try to get initial observation to verify connection
    try:
        initial_obs = robot_station.get_obs()
        logger.info(f"Initial observation keys: {list(initial_obs.keys())}")
        if 'arm_joints' in initial_obs:
            logger.info(f"Initial arm joints: {initial_obs['arm_joints']}")
        if 'hand_joints' in initial_obs:
            logger.info(f"Initial hand joints: {initial_obs['hand_joints']}")
    except Exception as e:
        logger.error(f"Could not get initial observation: {e}")
        logger.error("This error suggests that one or more robot components are not properly connected.")
        logger.error("Please check:")
        logger.error("1. Robot hardware is powered on and connected")
        logger.error("2. ROS2 topics are publishing joint position data")
        logger.error("3. Gripper/hand devices are connected and responding")
        logger.error("4. Network connectivity to robot controllers")

        # Don't continue if we can't get basic observations
        logger.error("Cannot proceed with deployment due to robot connection issues.")
        return
    
    # Load ACT model
    logger.info("Loading ACT model...")
    act_model = load_act_model(act_checkpoint_dir)

    # Get model device
    device = next(act_model.parameters()).device
    logger.info(f"ACT model is on device: {device}")

    # Check normalization statistics
    if debug:
        logger.info("=== Normalization Statistics ===")
        if hasattr(act_model, 'unnormalize_outputs'):
            unnorm = act_model.unnormalize_outputs
            if hasattr(unnorm, 'action_mean') and hasattr(unnorm, 'action_std'):
                logger.info(f"Action mean: {unnorm.action_mean}")
                logger.info(f"Action std: {unnorm.action_std}")
                logger.info(f"Action range estimate: [{(unnorm.action_mean - 2*unnorm.action_std).min():.3f}, {(unnorm.action_mean + 2*unnorm.action_std).max():.3f}]")
            else:
                logger.info("No action normalization statistics found in model")
        else:
            logger.info("No unnormalize_outputs found in model")

    # Setup debug directory
    if debug:
        debug_dir = f"debug_images_{int(time.time())}"
        logger.info(f"Debug mode enabled. Images and state info will be saved to: {debug_dir}")
    else:
        debug_dir = None

    try:
        for step in range(max_steps):
            logger.info(f"=== Step {step + 1}/{max_steps} ===")

            # Get observation from camera and sensors
            try:
                raw_obs = robot_station.get_obs()
            except Exception as e:
                logger.error(f"Failed to get observation at step {step + 1}: {e}")
                logger.error("This usually indicates a robot connection or hardware issue.")

                # Try to diagnose the specific issue
                if "'NoneType' object has no attribute 'get_radian_ndarray'" in str(e):
                    logger.error("Specific issue: Robot or gripper is returning None for joint positions.")
                    logger.error("Possible causes:")
                    logger.error("- Robot hardware is not connected or powered off")
                    logger.error("- ROS2 topics are not publishing joint position data")
                    logger.error("- Gripper/hand devices are not responding")
                    logger.error("- Network connectivity issues")

                # Skip this step and continue
                logger.info("Skipping this step and continuing...")
                time.sleep(interval)
                continue

            if debug:
                logger.info(f"Raw observation keys: {list(raw_obs.keys())}")
                for key, value in raw_obs.items():
                    if isinstance(value, dict):
                        logger.info(f"  {key}: {list(value.keys())}")
                    else:
                        logger.info(f"  {key}: {type(value)}")

            # Show camera images in real-time if requested
            if show_images and "images" in raw_obs and raw_obs["images"]:
                try:
                    for cam_name, img in raw_obs["images"].items():
                        if isinstance(img, np.ndarray) and img.shape[2] == 3:
                            # Create a window for each camera
                            window_name = f"Camera: {cam_name}"
                            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                            cv2.resizeWindow(window_name, 640, 480)

                            # Add text overlay with step info
                            img_with_text = img.copy()
                            cv2.putText(img_with_text, f"Step: {step+1}/{max_steps}",
                                      (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                            cv2.putText(img_with_text, f"Camera: {cam_name}",
                                      (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                            cv2.imshow(window_name, img_with_text)

                    # Allow OpenCV to process window events
                    cv2.waitKey(1)

                except cv2.error as e:
                    if step == 0:  # Only warn once
                        logger.warning(f"Cannot display images - no GUI support: {e}")
                        logger.info("Image display disabled. Use --debug to save images to files instead.")
                    show_images = False  # Disable for remaining steps

            # Validate observations before processing
            if not raw_obs:
                logger.error(f"Empty observation received at step {step + 1}")
                time.sleep(interval)
                continue

            # Check for None values in critical observation components
            if 'arm_joints' in raw_obs and raw_obs['arm_joints']:
                for arm_name, joints in raw_obs['arm_joints'].items():
                    if joints is None:
                        logger.error(f"Arm '{arm_name}' returned None joint positions at step {step + 1}")
                        logger.error("Cannot proceed with ACT inference without valid joint positions")
                        time.sleep(interval)
                        continue

            if 'hand_joints' in raw_obs and raw_obs['hand_joints']:
                for hand_name, joints in raw_obs['hand_joints'].items():
                    if joints is None:
                        logger.error(f"Hand '{hand_name}' returned None joint positions at step {step + 1}")
                        logger.error("Cannot proceed with ACT inference without valid joint positions")
                        time.sleep(interval)
                        continue

            # Transform observation for ACT model
            try:
                processed_obs = preprocess_xrocs_observation(raw_obs, debug=debug, debug_dir=debug_dir)
            except Exception as e:
                logger.error(f"Failed to preprocess observation at step {step + 1}: {e}")
                time.sleep(interval)
                continue

            # Move processed observations to model device
            for key, value in processed_obs.items():
                if isinstance(value, torch.Tensor):
                    processed_obs[key] = value.to(device)
                    if debug and step == 0:  # Only log for first step
                        logger.info(f"Moved {key} to device: {device}")

            # Get action chunk from ACT model using predict_action_chunk method
            with torch.no_grad():
                # Use predict_action_chunk method like in act_eval_episode.py
                action_chunk = act_model.predict_action_chunk(processed_obs)

                # Extract the specified action from the chunk
                # action_index is passed as parameter (0-based indexing)
                if len(action_chunk.shape) == 3:  # [batch, chunk_size, action_dim]
                    if action_index < action_chunk.shape[1]:
                        action = action_chunk[0, action_index, :].cpu().numpy()  # Take 10th action from chunk
                    else:
                        logger.warning(f"Action index {action_index} out of range, using last action")
                        action = action_chunk[0, -1, :].cpu().numpy()  # Use last action if index too large
                elif len(action_chunk.shape) == 2:  # [chunk_size, action_dim]
                    if action_index < action_chunk.shape[0]:
                        action = action_chunk[action_index, :].cpu().numpy()  # Take 10th action
                    else:
                        logger.warning(f"Action index {action_index} out of range, using last action")
                        action = action_chunk[-1, :].cpu().numpy()  # Use last action if index too large
                else:  # [action_dim] single action
                    action = action_chunk.cpu().numpy()

            if debug:
                logger.info(f"Predicted action chunk: shape={action_chunk.shape}, dtype={action_chunk.dtype}")
                logger.info(f"Action chunk range: [{action_chunk.min().item():.4f}, {action_chunk.max().item():.4f}]")
                logger.info(f"Using action index {action_index} (action #{action_index+1} out of {action_chunk.shape[1] if len(action_chunk.shape) == 3 else action_chunk.shape[0]})")
                logger.info(f"Current step action: shape={action.shape}, values={action}")

                # Action statistics
                logger.info(f"Action statistics:")
                logger.info(f"  Min: {action.min():.6f}, Max: {action.max():.6f}")
                logger.info(f"  Mean: {action.mean():.6f}, Std: {action.std():.6f}")
                logger.info(f"  Abs mean: {np.abs(action).mean():.6f}, Abs max: {np.abs(action).max():.6f}")

            # Apply action scaling if specified
            if action_scale != 1.0:
                # Calculate the difference from current position
                if 'arm_joints' in raw_obs and 'robot' in raw_obs['arm_joints']:
                    current_joints = raw_obs['arm_joints']['robot']
                    action_diff = action[:len(current_joints)] - current_joints
                    scaled_action_diff = action_diff * action_scale
                    action[:len(current_joints)] = current_joints + scaled_action_diff

                    if debug:
                        logger.info(f"Action scaling applied (factor: {action_scale})")
                        logger.info(f"Original action diff: max={np.abs(action_diff).max():.6f}")
                        logger.info(f"Scaled action diff: max={np.abs(scaled_action_diff).max():.6f}")

            # Decompose action for xrocs format
            if debug:
                logger.info(f"Input action type: {type(action)}")
                logger.info(f"Input action shape: {action.shape}")
                logger.info(f"Input action dtype: {action.dtype}")

            xrocs_action = robot_station.decompose_action(action)

            if debug:
                logger.info(f"xROCS action format: {xrocs_action}")
                logger.info(f"xROCS action type: {type(xrocs_action)}")

                # Check each component
                if isinstance(xrocs_action, dict):
                    for key, value in xrocs_action.items():
                        logger.info(f"  {key}: type={type(value)}, content={value}")
                        if isinstance(value, dict):
                            for subkey, subvalue in value.items():
                                logger.info(f"    {subkey}: type={type(subvalue)}, shape={getattr(subvalue, 'shape', 'N/A')}, dtype={getattr(subvalue, 'dtype', 'N/A')}")

                # Compare current state with target action
                if 'arm_joints' in raw_obs and 'robot' in raw_obs['arm_joints']:
                    current_joints = raw_obs['arm_joints']['robot']
                    target_joints = xrocs_action['arm_joints']['robot']
                    joint_diff = target_joints - current_joints
                    logger.info(f"Joint position comparison:")
                    logger.info(f"  Current joints: {current_joints}")
                    logger.info(f"  Target joints:  {target_joints}")
                    logger.info(f"  Differences:    {joint_diff}")
                    logger.info(f"  Max abs diff:   {np.abs(joint_diff).max():.6f}")
                    logger.info(f"  Mean abs diff:  {np.abs(joint_diff).mean():.6f}")

                # Check hand joint changes
                if 'hand_joints' in raw_obs:
                    for hand_name in ['left', 'right']:
                        if hand_name in raw_obs['hand_joints'] and hand_name in xrocs_action['hand_joints']:
                            current_hand = raw_obs['hand_joints'][hand_name]
                            target_hand = xrocs_action['hand_joints'][hand_name]
                            hand_diff = target_hand - current_hand
                            logger.info(f"  {hand_name} hand: current={current_hand}, target={target_hand}, diff={hand_diff}")
                # Save action info to file
                if debug_dir:
                    action_file = os.path.join(debug_dir, f"action_step_{step+1:04d}.txt")
                    with open(action_file, "w") as f:
                        f.write(f"Step {step + 1} Action Information:\n")
                        f.write("=" * 50 + "\n")
                        f.write(f"Action chunk shape: {action_chunk.shape}\n")
                        f.write(f"Action chunk range: [{action_chunk.min().item():.4f}, {action_chunk.max().item():.4f}]\n")
                        f.write(f"Using action index: {action_index} (action #{action_index+1} out of {action_chunk.shape[1] if len(action_chunk.shape) == 3 else action_chunk.shape[0]})\n")
                        f.write(f"Current step action shape: {action.shape}\n")
                        f.write(f"Current step action values: {action}\n")
                        f.write(f"xROCS action format: {xrocs_action}\n")
                        f.write("\nFull action chunk:\n")
                        f.write(f"{action_chunk.cpu().numpy()}\n")

            # Execute action
            try:
                result = robot_station.step(xrocs_action)
                logger.info(f"Step {step + 1}/{max_steps} executed. Result: {result}")

                # Check if robot_station has any status methods
                if hasattr(robot_station, 'get_status'):
                    status = robot_station.get_status()
                    logger.info(f"Robot status: {status}")

            except Exception as e:
                logger.error(f"Error executing step {step + 1}: {e}")
                if debug:
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                break
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user.")
    except Exception as e:
        logger.error(f"Error during deployment: {str(e)}")
    finally:
        # Cleanup
        if show_images:
            try:
                cv2.destroyAllWindows()
                logger.info("Closed all image windows.")
            except:
                pass  # Ignore errors during cleanup
        robot_station.close()
        logger.info("Deployment finished.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Deploy ACT model using xROCS API")
    parser.add_argument("xrocs_config_path", help="Path to xROCS configuration file")
    parser.add_argument("act_checkpoint_dir", help="Directory containing ACT model checkpoint")
    parser.add_argument("--max_steps", type=int, default=1000, help="Maximum number of steps to run (default: 1000)")
    parser.add_argument("--interval", type=float, default=0.05, help="Time interval between steps in seconds (default: 0.05)")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode with image saving and detailed logging")
    parser.add_argument("--show_images", action="store_true", help="Show camera images in real-time windows")
    parser.add_argument("--action_index", type=int, default=0, help="Which action to use from the 20-action chunk (0-19, default: 0)")
    parser.add_argument("--action_scale", type=float, default=1.0, help="Scale factor for actions (default: 1.0, use >1.0 to amplify actions)")

    args = parser.parse_args()

    if args.debug:
        logger.info("Debug mode enabled!")
        logger.info(f"xROCS config: {args.xrocs_config_path}")
        logger.info(f"ACT checkpoint: {args.act_checkpoint_dir}")
        logger.info(f"Max steps: {args.max_steps}")
        logger.info(f"Interval: {args.interval}s")

    # Validate action_index
    if args.action_index < 0 or args.action_index >= 20:
        print(f"Error: action_index must be between 0 and 19, got {args.action_index}")
        sys.exit(1)

    if args.debug:
        logger.info(f"Using action index: {args.action_index} (action #{args.action_index+1} out of 20)")

    run_act_deploy(
        xrocs_config_path=args.xrocs_config_path,
        act_checkpoint_dir=args.act_checkpoint_dir,
        max_steps=args.max_steps,
        interval=args.interval,
        debug=args.debug,
        show_images=args.show_images,
        action_index=args.action_index,
        action_scale=args.action_scale
    )
